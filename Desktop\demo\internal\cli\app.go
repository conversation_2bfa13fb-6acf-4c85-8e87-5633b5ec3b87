package cli

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"ollama-cli/internal/config"
	"ollama-cli/internal/ollama"
)

// App CLI应用程序
type App struct {
	config *config.Config
	client *ollama.Client
}

// NewApp 创建新的CLI应用
func NewApp(cfg *config.Config) *App {
	timeout := time.Duration(cfg.Timeout) * time.Second
	client := ollama.NewClient(cfg.Host, timeout, cfg.Verbose)
	
	return &App{
		config: cfg,
		client: client,
	}
}

// Run 运行CLI命令
func (a *App) Run(args []string) error {
	if len(args) == 0 {
		return fmt.Errorf("未指定命令")
	}

	command := args[0]
	ctx := context.Background()

	switch command {
	case "list":
		return a.listModels(ctx)
	case "pull":
		if len(args) < 2 {
			return fmt.Errorf("pull命令需要指定模型名称")
		}
		return a.pullModel(ctx, args[1])
	case "chat":
		if len(args) < 2 {
			return fmt.Errorf("chat命令需要指定模型名称")
		}
		return a.chatWithModel(ctx, args[1])
	case "generate":
		if len(args) < 3 {
			return fmt.Errorf("generate命令需要指定模型名称和提示词")
		}
		prompt := strings.Join(args[2:], " ")
		return a.generateText(ctx, args[1], prompt)
	case "show":
		if len(args) < 2 {
			return fmt.Errorf("show命令需要指定模型名称")
		}
		return a.showModel(ctx, args[1])
	case "delete":
		if len(args) < 2 {
			return fmt.Errorf("delete命令需要指定模型名称")
		}
		return a.deleteModel(ctx, args[1])
	default:
		return fmt.Errorf("未知命令: %s", command)
	}
}

// listModels 列出所有模型
func (a *App) listModels(ctx context.Context) error {
	resp, err := a.client.ListModels(ctx)
	if err != nil {
		return fmt.Errorf("获取模型列表失败: %w", err)
	}

	if len(resp.Models) == 0 {
		fmt.Println("没有找到任何模型")
		return nil
	}

	fmt.Printf("%-30s %-15s %-20s %s\n", "模型名称", "大小", "修改时间", "摘要")
	fmt.Println(strings.Repeat("-", 80))

	for _, model := range resp.Models {
		size := formatSize(model.Size)
		modTime := model.ModifiedAt.Format("2006-01-02 15:04")
		digest := model.Digest
		if len(digest) > 12 {
			digest = digest[:12] + "..."
		}
		
		fmt.Printf("%-30s %-15s %-20s %s\n", model.Name, size, modTime, digest)
	}

	return nil
}

// pullModel 下载模型
func (a *App) pullModel(ctx context.Context, model string) error {
	fmt.Printf("正在下载模型: %s\n", model)
	return a.client.PullModel(ctx, model, true)
}

// chatWithModel 与模型对话
func (a *App) chatWithModel(ctx context.Context, model string) error {
	fmt.Printf("开始与模型 %s 对话 (输入 'exit' 退出)\n", model)
	fmt.Println(strings.Repeat("-", 50))

	scanner := bufio.NewScanner(os.Stdin)
	var messages []ollama.ChatMessage

	for {
		fmt.Print("用户: ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "exit" {
			fmt.Println("对话结束")
			break
		}

		if input == "" {
			continue
		}

		// 添加用户消息
		messages = append(messages, ollama.ChatMessage{
			Role:    "user",
			Content: input,
		})

		// 发送聊天请求
		resp, err := a.client.Chat(ctx, model, messages, false)
		if err != nil {
			fmt.Printf("聊天失败: %v\n", err)
			continue
		}

		// 显示助手回复
		fmt.Printf("助手: %s\n\n", resp.Message.Content)

		// 添加助手消息到历史
		messages = append(messages, resp.Message)
	}

	return scanner.Err()
}

// generateText 生成文本
func (a *App) generateText(ctx context.Context, model, prompt string) error {
	fmt.Printf("使用模型 %s 生成文本...\n", model)
	
	resp, err := a.client.Generate(ctx, model, prompt, false)
	if err != nil {
		return fmt.Errorf("生成文本失败: %w", err)
	}

	fmt.Println("生成结果:")
	fmt.Println(strings.Repeat("-", 50))
	fmt.Println(resp.Response)
	
	if a.config.Verbose && resp.Done {
		fmt.Println(strings.Repeat("-", 50))
		fmt.Printf("统计信息:\n")
		fmt.Printf("  总耗时: %s\n", time.Duration(resp.TotalDuration))
		fmt.Printf("  加载耗时: %s\n", time.Duration(resp.LoadDuration))
		if resp.PromptEvalCount > 0 {
			fmt.Printf("  提示词评估: %d tokens, %s\n", resp.PromptEvalCount, time.Duration(resp.PromptEvalDuration))
		}
		if resp.EvalCount > 0 {
			fmt.Printf("  生成评估: %d tokens, %s\n", resp.EvalCount, time.Duration(resp.EvalDuration))
		}
	}

	return nil
}

// showModel 显示模型信息
func (a *App) showModel(ctx context.Context, model string) error {
	resp, err := a.client.ShowModel(ctx, model)
	if err != nil {
		return fmt.Errorf("获取模型信息失败: %w", err)
	}

	fmt.Printf("模型信息: %s\n", model)
	fmt.Println(strings.Repeat("=", 50))
	
	fmt.Printf("格式: %s\n", resp.Details.Format)
	fmt.Printf("家族: %s\n", resp.Details.Family)
	fmt.Printf("参数大小: %s\n", resp.Details.ParameterSize)
	fmt.Printf("量化级别: %s\n", resp.Details.QuantizationLevel)
	
	if resp.License != "" {
		fmt.Printf("\n许可证:\n%s\n", resp.License)
	}
	
	if resp.Template != "" {
		fmt.Printf("\n模板:\n%s\n", resp.Template)
	}
	
	if a.config.Verbose && resp.Modelfile != "" {
		fmt.Printf("\nModelfile:\n%s\n", resp.Modelfile)
	}

	return nil
}

// deleteModel 删除模型
func (a *App) deleteModel(ctx context.Context, model string) error {
	fmt.Printf("确定要删除模型 %s 吗? (y/N): ", model)
	
	scanner := bufio.NewScanner(os.Stdin)
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}
	
	input := strings.ToLower(strings.TrimSpace(scanner.Text()))
	if input != "y" && input != "yes" {
		fmt.Println("取消删除")
		return nil
	}

	return a.client.DeleteModel(ctx, model)
}

// formatSize 格式化文件大小
func formatSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
