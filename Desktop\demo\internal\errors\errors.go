package errors

import (
	"fmt"
	"net"
	"net/url"
	"strings"
)

// ErrorType 错误类型
type ErrorType int

const (
	// NetworkError 网络错误
	NetworkError ErrorType = iota
	// APIError API错误
	APIError
	// ConfigError 配置错误
	ConfigError
	// ValidationError 验证错误
	ValidationError
	// UnknownError 未知错误
	UnknownError
)

// AppError 应用程序错误
type AppError struct {
	Type    ErrorType
	Message string
	Cause   error
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Cause)
	}
	return e.Message
}

// Unwrap 返回原始错误
func (e *AppError) Unwrap() error {
	return e.Cause
}

// NewNetworkError 创建网络错误
func NewNetworkError(message string, cause error) *AppError {
	return &AppError{
		Type:    NetworkError,
		Message: message,
		Cause:   cause,
	}
}

// NewAPIError 创建API错误
func NewAPIError(message string, cause error) *AppError {
	return &AppError{
		Type:    APIError,
		Message: message,
		Cause:   cause,
	}
}

// NewConfigError 创建配置错误
func NewConfigError(message string, cause error) *AppError {
	return &AppError{
		Type:    ConfigError,
		Message: message,
		Cause:   cause,
	}
}

// NewValidationError 创建验证错误
func NewValidationError(message string) *AppError {
	return &AppError{
		Type:    ValidationError,
		Message: message,
	}
}

// WrapError 包装错误并自动判断类型
func WrapError(err error, message string) *AppError {
	if err == nil {
		return nil
	}

	// 判断错误类型
	var errorType ErrorType
	switch {
	case isNetworkError(err):
		errorType = NetworkError
	case isAPIError(err):
		errorType = APIError
	case isConfigError(err):
		errorType = ConfigError
	default:
		errorType = UnknownError
	}

	return &AppError{
		Type:    errorType,
		Message: message,
		Cause:   err,
	}
}

// isNetworkError 判断是否为网络错误
func isNetworkError(err error) bool {
	if err == nil {
		return false
	}

	// 检查网络相关错误
	if _, ok := err.(*net.OpError); ok {
		return true
	}
	if _, ok := err.(*url.Error); ok {
		return true
	}
	if _, ok := err.(net.Error); ok {
		return true
	}

	// 检查错误消息中的网络关键词
	errMsg := strings.ToLower(err.Error())
	networkKeywords := []string{
		"connection refused",
		"connection timeout",
		"network unreachable",
		"no such host",
		"dial tcp",
		"i/o timeout",
	}

	for _, keyword := range networkKeywords {
		if strings.Contains(errMsg, keyword) {
			return true
		}
	}

	return false
}

// isAPIError 判断是否为API错误
func isAPIError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := strings.ToLower(err.Error())
	apiKeywords := []string{
		"http",
		"status code",
		"json",
		"unmarshal",
		"api",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(errMsg, keyword) {
			return true
		}
	}

	return false
}

// isConfigError 判断是否为配置错误
func isConfigError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := strings.ToLower(err.Error())
	configKeywords := []string{
		"config",
		"configuration",
		"invalid url",
		"parse",
	}

	for _, keyword := range configKeywords {
		if strings.Contains(errMsg, keyword) {
			return true
		}
	}

	return false
}

// GetUserFriendlyMessage 获取用户友好的错误消息
func GetUserFriendlyMessage(err error) string {
	if err == nil {
		return ""
	}

	if appErr, ok := err.(*AppError); ok {
		switch appErr.Type {
		case NetworkError:
			return "网络连接失败，请检查网络连接和Ollama服务器状态"
		case APIError:
			return "API调用失败，请检查请求参数和服务器响应"
		case ConfigError:
			return "配置错误，请检查配置文件和参数设置"
		case ValidationError:
			return "参数验证失败，请检查输入参数"
		default:
			return "发生未知错误"
		}
	}

	return err.Error()
}
