package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"ollama-cli/internal/cli"
	"ollama-cli/internal/config"
)

func main() {
	// 设置日志格式
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// 解析命令行参数
	var (
		configFile = flag.String("config", "", "配置文件路径")
		host       = flag.String("host", "http://localhost:11434", "Ollama服务器地址")
		verbose    = flag.Bool("verbose", false, "启用详细输出")
		version    = flag.Bool("version", false, "显示版本信息")
	)
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Println("Ollama CLI v1.0.0")
		fmt.Println("一个轻量级的Ollama API客户端工具")
		return
	}

	// 加载配置
	cfg, err := config.Load(*configFile, *host, *verbose)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 创建CLI应用
	app := cli.NewApp(cfg)

	// 获取命令参数
	args := flag.Args()
	if len(args) == 0 {
		fmt.Println("使用方法: ollama-cli [选项] <命令> [参数...]")
		fmt.Println()
		fmt.Println("可用命令:")
		fmt.Println("  list                    列出所有可用模型")
		fmt.Println("  pull <model>           下载模型")
		fmt.Println("  chat <model>           与模型对话")
		fmt.Println("  generate <model> <prompt>  生成文本")
		fmt.Println("  stream-generate <model> <prompt>  流式生成文本")
		fmt.Println("  show <model>           显示模型信息")
		fmt.Println("  delete <model>         删除模型")
		fmt.Println()
		fmt.Println("会话管理:")
		fmt.Println("  sessions list          列出所有会话")
		fmt.Println("  sessions create <name> <model>  创建新会话")
		fmt.Println("  sessions delete <id>   删除会话")
		fmt.Println("  sessions show <id>     显示会话详情")
		fmt.Println("  sessions stats <id>    显示会话统计")
		fmt.Println("  sessions export <id> <path>  导出会话")
		fmt.Println("  sessions import <path> 导入会话")
		fmt.Println("  chat-session <id>      使用会话对话")
		fmt.Println()
		fmt.Println("批处理:")
		fmt.Println("  batch list             列出批处理任务")
		fmt.Println("  batch run <model> <file>  运行批处理任务")
		fmt.Println("  batch show <id>        显示任务详情")
		fmt.Println("  batch export <id> <file>  导出任务结果")
		fmt.Println()
		fmt.Println("文件操作:")
		fmt.Println("  file template <file>   创建提示词模板")
		fmt.Println("  file validate <file>   验证文件格式")
		fmt.Println("  file info <file>       显示文件信息")
		fmt.Println("  file backup <file>     备份文件")
		fmt.Println("  file cleanup           清理旧文件")
		fmt.Println()
		fmt.Println("模板管理:")
		fmt.Println("  template list          列出所有模板")
		fmt.Println("  template show <id>     显示模板详情")
		fmt.Println("  template use <id>      使用模板生成内容")
		fmt.Println("  template create        创建新模板")
		fmt.Println("  template delete <id>   删除模板")
		fmt.Println("  template search <query> 搜索模板")
		fmt.Println("  template export <id> <file> 导出模板")
		fmt.Println("  template import <file> 导入模板")
		fmt.Println()
		fmt.Println("选项:")
		flag.PrintDefaults()
		os.Exit(1)
	}

	// 执行命令
	if err := app.Run(args); err != nil {
		log.Fatalf("执行命令失败: %v", err)
	}
}
