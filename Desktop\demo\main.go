package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"ollama-cli/internal/cli"
	"ollama-cli/internal/config"
)

func main() {
	// 设置日志格式
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// 解析命令行参数
	var (
		configFile = flag.String("config", "", "配置文件路径")
		host       = flag.String("host", "http://localhost:11434", "Ollama服务器地址")
		verbose    = flag.Bool("verbose", false, "启用详细输出")
		version    = flag.Bool("version", false, "显示版本信息")
	)
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Println("Ollama CLI v1.0.0")
		fmt.Println("一个轻量级的Ollama API客户端工具")
		return
	}

	// 加载配置
	cfg, err := config.Load(*configFile, *host, *verbose)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 创建CLI应用
	app := cli.NewApp(cfg)

	// 获取命令参数
	args := flag.Args()
	if len(args) == 0 {
		fmt.Println("使用方法: ollama-cli [选项] <命令> [参数...]")
		fmt.Println()
		fmt.Println("可用命令:")
		fmt.Println("  list                    列出所有可用模型")
		fmt.Println("  pull <model>           下载模型")
		fmt.Println("  chat <model>           与模型对话")
		fmt.Println("  generate <model> <prompt>  生成文本")
		fmt.Println("  show <model>           显示模型信息")
		fmt.Println("  delete <model>         删除模型")
		fmt.Println()
		fmt.Println("选项:")
		flag.PrintDefaults()
		os.Exit(1)
	}

	// 执行命令
	if err := app.Run(args); err != nil {
		log.Fatalf("执行命令失败: %v", err)
	}
}
