package config

import (
	"os"
	"path/filepath"
	"testing"
)

func TestDefaultConfig(t *testing.T) {
	cfg := DefaultConfig()
	
	if cfg.Host != "http://localhost:11434" {
		t.<PERSON><PERSON><PERSON>("期望默认主机为 'http://localhost:11434', 实际为 '%s'", cfg.Host)
	}
	
	if cfg.Verbose != false {
		t.<PERSON><PERSON>("期望默认verbose为false, 实际为 %v", cfg.Verbose)
	}
	
	if cfg.Timeout != 30 {
		t.<PERSON><PERSON><PERSON>("期望默认超时为30秒, 实际为 %d", cfg.Timeout)
	}
}

func TestLoadFromFile(t *testing.T) {
	// 创建临时配置文件
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "test-config.json")
	
	configContent := `{
		"host": "http://test.example.com:8080",
		"verbose": true,
		"timeout": 60
	}`
	
	err := os.WriteFile(configFile, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("创建测试配置文件失败: %v", err)
	}
	
	// 测试加载配置
	cfg, err := Load(configFile, "http://localhost:11434", false)
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}
	
	if cfg.Host != "http://test.example.com:8080" {
		t.Errorf("期望主机为 'http://test.example.com:8080', 实际为 '%s'", cfg.Host)
	}
	
	if cfg.Verbose != true {
		t.Errorf("期望verbose为true, 实际为 %v", cfg.Verbose)
	}
	
	if cfg.Timeout != 60 {
		t.Errorf("期望超时为60秒, 实际为 %d", cfg.Timeout)
	}
}

func TestLoadWithCommandLineOverride(t *testing.T) {
	// 创建临时配置文件
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "test-config.json")
	
	configContent := `{
		"host": "http://test.example.com:8080",
		"verbose": false,
		"timeout": 60
	}`
	
	err := os.WriteFile(configFile, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("创建测试配置文件失败: %v", err)
	}
	
	// 测试命令行参数覆盖配置文件
	cfg, err := Load(configFile, "http://override.example.com:9090", true)
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}
	
	// 命令行参数应该覆盖配置文件
	if cfg.Host != "http://override.example.com:9090" {
		t.Errorf("期望主机为 'http://override.example.com:9090', 实际为 '%s'", cfg.Host)
	}
	
	if cfg.Verbose != true {
		t.Errorf("期望verbose为true, 实际为 %v", cfg.Verbose)
	}
	
	// 配置文件中的值应该保留
	if cfg.Timeout != 60 {
		t.Errorf("期望超时为60秒, 实际为 %d", cfg.Timeout)
	}
}

func TestLoadNonExistentFile(t *testing.T) {
	// 测试加载不存在的配置文件
	cfg, err := Load("", "http://localhost:11434", false)
	if err != nil {
		t.Fatalf("加载默认配置失败: %v", err)
	}
	
	// 应该返回默认配置
	if cfg.Host != "http://localhost:11434" {
		t.Errorf("期望默认主机为 'http://localhost:11434', 实际为 '%s'", cfg.Host)
	}
}

func TestSaveConfig(t *testing.T) {
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "save-test.json")
	
	cfg := &Config{
		Host:    "http://save.test.com:8080",
		Verbose: true,
		Timeout: 45,
	}
	
	err := cfg.Save(configFile)
	if err != nil {
		t.Fatalf("保存配置失败: %v", err)
	}
	
	// 验证文件是否存在
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		t.Fatalf("配置文件未创建")
	}
	
	// 重新加载并验证
	loadedCfg, err := Load(configFile, "http://localhost:11434", false)
	if err != nil {
		t.Fatalf("重新加载配置失败: %v", err)
	}
	
	if loadedCfg.Host != cfg.Host {
		t.Errorf("期望主机为 '%s', 实际为 '%s'", cfg.Host, loadedCfg.Host)
	}
	
	if loadedCfg.Verbose != cfg.Verbose {
		t.Errorf("期望verbose为 %v, 实际为 %v", cfg.Verbose, loadedCfg.Verbose)
	}
	
	if loadedCfg.Timeout != cfg.Timeout {
		t.Errorf("期望超时为 %d, 实际为 %d", cfg.Timeout, loadedCfg.Timeout)
	}
}
