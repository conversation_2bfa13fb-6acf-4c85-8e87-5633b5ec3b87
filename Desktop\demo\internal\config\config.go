package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// Config 应用配置结构
type Config struct {
	Host    string `json:"host"`
	Verbose bool   `json:"verbose"`
	Timeout int    `json:"timeout"` // 请求超时时间（秒）
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Host:    "http://localhost:11434",
		Verbose: false,
		Timeout: 30,
	}
}

// Load 加载配置文件
func Load(configFile, host string, verbose bool) (*Config, error) {
	cfg := DefaultConfig()

	// 如果指定了配置文件，尝试加载
	if configFile != "" {
		if err := loadFromFile(cfg, configFile); err != nil {
			return nil, fmt.Errorf("加载配置文件失败: %w", err)
		}
	} else {
		// 尝试从默认位置加载配置文件
		homeDir, err := os.UserHomeDir()
		if err == nil {
			defaultConfigPath := filepath.Join(homeDir, ".ollama-cli.json")
			if _, err := os.Stat(defaultConfigPath); err == nil {
				loadFromFile(cfg, defaultConfigPath)
			}
		}
	}

	// 命令行参数覆盖配置文件
	if host != "http://localhost:11434" {
		cfg.Host = host
	}
	if verbose {
		cfg.Verbose = verbose
	}

	return cfg, nil
}

// loadFromFile 从文件加载配置
func loadFromFile(cfg *Config, filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, cfg)
}

// Save 保存配置到文件
func (c *Config) Save(filename string) error {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}

	// 确保目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	return os.WriteFile(filename, data, 0644)
}
