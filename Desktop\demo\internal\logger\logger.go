package logger

import (
	"fmt"
	"log"
	"os"
)

// Logger 日志记录器
type Logger struct {
	verbose bool
	logger  *log.Logger
}

// New 创建新的日志记录器
func New(verbose bool) *Logger {
	return &Logger{
		verbose: verbose,
		logger:  log.New(os.Stderr, "", log.LstdFlags|log.Lshortfile),
	}
}

// Info 记录信息日志
func (l *Logger) Info(format string, args ...interface{}) {
	if l.verbose {
		l.logger.Printf("[INFO] "+format, args...)
	}
}

// Debug 记录调试日志
func (l *Logger) Debug(format string, args ...interface{}) {
	if l.verbose {
		l.logger.Printf("[DEBUG] "+format, args...)
	}
}

// Warn 记录警告日志
func (l *Logger) Warn(format string, args ...interface{}) {
	l.logger.Printf("[WARN] "+format, args...)
}

// Error 记录错误日志
func (l *Logger) Error(format string, args ...interface{}) {
	l.logger.Printf("[ERROR] "+format, args...)
}

// Fatal 记录致命错误日志并退出
func (l *Logger) Fatal(format string, args ...interface{}) {
	l.logger.Printf("[FATAL] "+format, args...)
	os.Exit(1)
}

// Printf 格式化输出到标准输出
func Printf(format string, args ...interface{}) {
	fmt.Printf(format, args...)
}

// Println 输出到标准输出并换行
func Println(args ...interface{}) {
	fmt.Println(args...)
}
