# Ollama CLI

一个轻量级的Ollama API客户端工具，使用Go语言开发，仅依赖标准库。

## 功能特性

- 🚀 轻量级设计，仅使用Go标准库
- 🔧 完整的Ollama API支持
- 💬 交互式聊天模式
- 📝 文本生成功能
- 📋 模型管理（列表、下载、删除、查看）
- ⚙️ 灵活的配置选项
- 🛡️ 完善的错误处理和日志记录
- ✅ 全面的单元测试覆盖

## 安装

### 从源码构建

```bash
git clone <repository-url>
cd ollama-cli
go build -o ollama-cli main.go
```

### 直接运行

```bash
go run main.go [命令] [参数...]
```

## 使用方法

### 基本命令

```bash
# 显示帮助信息
./ollama-cli

# 显示版本信息
./ollama-cli -version

# 列出所有可用模型
./ollama-cli list

# 下载模型
./ollama-cli pull llama2

# 与模型对话
./ollama-cli chat llama2

# 生成文本
./ollama-cli generate llama2 "写一首关于春天的诗"

# 显示模型信息
./ollama-cli show llama2

# 删除模型
./ollama-cli delete llama2
```

### 命令行选项

```bash
-config string    配置文件路径
-host string      Ollama服务器地址 (默认: "http://localhost:11434")
-verbose          启用详细输出
-version          显示版本信息
```

### 配置文件

创建配置文件 `~/.ollama-cli.json`：

```json
{
  "host": "http://localhost:11434",
  "verbose": false,
  "timeout": 30
}
```

或者使用自定义配置文件：

```bash
./ollama-cli -config /path/to/config.json list
```

## 使用示例

### 1. 列出模型

```bash
$ ./ollama-cli list
模型名称                        大小            修改时间             摘要
--------------------------------------------------------------------------------
llama2:latest                  3.8 GB          2024-01-15 10:30     sha256:abc123...
codellama:7b                   3.8 GB          2024-01-14 15:45     sha256:def456...
```

### 2. 交互式聊天

```bash
$ ./ollama-cli chat llama2
开始与模型 llama2 对话 (输入 'exit' 退出)
--------------------------------------------------
用户: 你好，请介绍一下自己
助手: 你好！我是一个AI助手，基于Llama 2模型。我可以帮助你回答问题、进行对话、协助写作等。有什么我可以帮助你的吗？

用户: exit
对话结束
```

### 3. 生成文本

```bash
$ ./ollama-cli generate llama2 "解释什么是机器学习"
使用模型 llama2 生成文本...
生成结果:
--------------------------------------------------
机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。通过分析大量数据，机器学习算法可以识别模式、做出预测和决策...
```

### 4. 详细输出模式

```bash
$ ./ollama-cli -verbose generate llama2 "Hello"
请求URL: http://localhost:11434/api/generate
请求数据: {"model":"llama2","prompt":"Hello","stream":false}
使用模型 llama2 生成文本...
生成结果:
--------------------------------------------------
Hello! How can I help you today?
--------------------------------------------------
统计信息:
  总耗时: 2.5s
  加载耗时: 500ms
  提示词评估: 1 tokens, 100ms
  生成评估: 8 tokens, 1.9s
```

## 项目结构

```
ollama-cli/
├── main.go                    # 主程序入口
├── go.mod                     # Go模块文件
├── config.example.json        # 示例配置文件
├── README.md                  # 项目文档
├── internal/
│   ├── cli/                   # CLI应用逻辑
│   │   └── app.go
│   ├── config/                # 配置管理
│   │   ├── config.go
│   │   └── config_test.go
│   ├── ollama/                # Ollama API客户端
│   │   ├── client.go
│   │   ├── client_test.go
│   │   └── types.go
│   ├── errors/                # 错误处理
│   │   ├── errors.go
│   │   └── errors_test.go
│   └── logger/                # 日志记录
│       └── logger.go
```

## API支持

本工具支持以下Ollama API端点：

- `GET /api/tags` - 列出模型
- `POST /api/generate` - 生成文本
- `POST /api/chat` - 聊天对话
- `POST /api/pull` - 下载模型
- `POST /api/show` - 显示模型信息
- `DELETE /api/delete` - 删除模型

## 错误处理

工具提供了完善的错误处理机制：

- **网络错误**: 自动检测连接问题并提供友好提示
- **API错误**: 解析服务器响应并显示具体错误信息
- **配置错误**: 验证配置文件格式和参数有效性
- **验证错误**: 检查用户输入参数的合法性

## 测试

运行所有测试：

```bash
go test ./...
```

运行特定包的测试：

```bash
go test ./internal/config
go test ./internal/ollama
go test ./internal/errors
```

查看测试覆盖率：

```bash
go test -cover ./...
```

## 开发

### 添加新功能

1. 在相应的包中添加新功能
2. 编写对应的单元测试
3. 更新文档和示例

### 代码规范

- 使用Go标准格式化工具：`go fmt`
- 运行代码检查：`go vet`
- 确保所有测试通过：`go test ./...`

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 常见问题

### Q: 如何连接到远程Ollama服务器？

A: 使用 `-host` 参数或在配置文件中设置host字段：

```bash
./ollama-cli -host http://remote-server:11434 list
```

### Q: 如何启用详细日志？

A: 使用 `-verbose` 参数：

```bash
./ollama-cli -verbose chat llama2
```

### Q: 配置文件在哪里？

A: 默认配置文件位置为 `~/.ollama-cli.json`，也可以使用 `-config` 参数指定自定义位置。

### Q: 如何处理网络超时？

A: 在配置文件中设置timeout字段（单位：秒）：

```json
{
  "timeout": 60
}
```
