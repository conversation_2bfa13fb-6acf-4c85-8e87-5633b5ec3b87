package monitor

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	StartTime         time.Time     `json:"start_time"`
	EndTime           time.Time     `json:"end_time"`
	Duration          time.Duration `json:"duration"`
	TokensGenerated   int           `json:"tokens_generated"`
	TokensPerSecond   float64       `json:"tokens_per_second"`
	CharactersCount   int           `json:"characters_count"`
	WordsCount        int           `json:"words_count"`
	RequestCount      int           `json:"request_count"`
	ErrorCount        int           `json:"error_count"`
	AverageLatency    time.Duration `json:"average_latency"`
	ModelName         string        `json:"model_name"`
	PromptTokens      int           `json:"prompt_tokens"`
	CompletionTokens  int           `json:"completion_tokens"`
	TotalTokens       int           `json:"total_tokens"`
}

// Monitor 性能监控器
type Monitor struct {
	metrics map[string]*PerformanceMetrics
	mutex   sync.RWMutex
}

// NewMonitor 创建新的性能监控器
func NewMonitor() *Monitor {
	return &Monitor{
		metrics: make(map[string]*PerformanceMetrics),
	}
}

// StartSession 开始监控会话
func (m *Monitor) StartSession(sessionID, modelName string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.metrics[sessionID] = &PerformanceMetrics{
		StartTime:    time.Now(),
		ModelName:    modelName,
		RequestCount: 0,
		ErrorCount:   0,
	}
}

// EndSession 结束监控会话
func (m *Monitor) EndSession(sessionID string) *PerformanceMetrics {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if metrics, exists := m.metrics[sessionID]; exists {
		metrics.EndTime = time.Now()
		metrics.Duration = metrics.EndTime.Sub(metrics.StartTime)
		
		if metrics.Duration.Seconds() > 0 && metrics.TokensGenerated > 0 {
			metrics.TokensPerSecond = float64(metrics.TokensGenerated) / metrics.Duration.Seconds()
		}
		
		if metrics.RequestCount > 0 {
			metrics.AverageLatency = metrics.Duration / time.Duration(metrics.RequestCount)
		}

		// 返回副本并删除原始数据
		result := *metrics
		delete(m.metrics, sessionID)
		return &result
	}

	return nil
}

// RecordRequest 记录请求
func (m *Monitor) RecordRequest(sessionID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if metrics, exists := m.metrics[sessionID]; exists {
		metrics.RequestCount++
	}
}

// RecordError 记录错误
func (m *Monitor) RecordError(sessionID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if metrics, exists := m.metrics[sessionID]; exists {
		metrics.ErrorCount++
	}
}

// RecordTokens 记录token信息
func (m *Monitor) RecordTokens(sessionID string, promptTokens, completionTokens int) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if metrics, exists := m.metrics[sessionID]; exists {
		metrics.PromptTokens += promptTokens
		metrics.CompletionTokens += completionTokens
		metrics.TokensGenerated += completionTokens
		metrics.TotalTokens = metrics.PromptTokens + metrics.CompletionTokens
	}
}

// RecordText 记录文本信息
func (m *Monitor) RecordText(sessionID, text string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if metrics, exists := m.metrics[sessionID]; exists {
		metrics.CharactersCount += len(text)
		metrics.WordsCount += countWords(text)
	}
}

// GetCurrentMetrics 获取当前指标
func (m *Monitor) GetCurrentMetrics(sessionID string) *PerformanceMetrics {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if metrics, exists := m.metrics[sessionID]; exists {
		// 返回副本
		current := *metrics
		current.Duration = time.Since(metrics.StartTime)
		
		if current.Duration.Seconds() > 0 && current.TokensGenerated > 0 {
			current.TokensPerSecond = float64(current.TokensGenerated) / current.Duration.Seconds()
		}
		
		return &current
	}

	return nil
}

// PrintMetrics 打印性能指标
func (m *Monitor) PrintMetrics(metrics *PerformanceMetrics) {
	if metrics == nil {
		fmt.Println("没有性能数据")
		return
	}

	fmt.Println("性能统计:")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Printf("模型: %s\n", metrics.ModelName)
	fmt.Printf("总耗时: %s\n", metrics.Duration)
	fmt.Printf("请求次数: %d\n", metrics.RequestCount)
	
	if metrics.ErrorCount > 0 {
		fmt.Printf("错误次数: %d\n", metrics.ErrorCount)
		successRate := float64(metrics.RequestCount-metrics.ErrorCount) / float64(metrics.RequestCount) * 100
		fmt.Printf("成功率: %.1f%%\n", successRate)
	}
	
	if metrics.AverageLatency > 0 {
		fmt.Printf("平均延迟: %s\n", metrics.AverageLatency)
	}
	
	fmt.Printf("生成字符数: %d\n", metrics.CharactersCount)
	fmt.Printf("生成单词数: %d\n", metrics.WordsCount)
	
	if metrics.TokensGenerated > 0 {
		fmt.Printf("生成tokens: %d\n", metrics.TokensGenerated)
		fmt.Printf("提示tokens: %d\n", metrics.PromptTokens)
		fmt.Printf("总tokens: %d\n", metrics.TotalTokens)
		
		if metrics.TokensPerSecond > 0 {
			fmt.Printf("生成速度: %.1f tokens/秒\n", metrics.TokensPerSecond)
		}
		
		if metrics.CharactersCount > 0 {
			charsPerToken := float64(metrics.CharactersCount) / float64(metrics.TokensGenerated)
			fmt.Printf("平均字符/token: %.1f\n", charsPerToken)
		}
	}
	
	if metrics.Duration.Seconds() > 0 {
		charsPerSecond := float64(metrics.CharactersCount) / metrics.Duration.Seconds()
		fmt.Printf("字符生成速度: %.1f 字符/秒\n", charsPerSecond)
	}
}

// PrintCompactMetrics 打印紧凑的性能指标
func (m *Monitor) PrintCompactMetrics(metrics *PerformanceMetrics) {
	if metrics == nil {
		return
	}

	fmt.Printf("⏱️  耗时: %s", metrics.Duration)
	
	if metrics.TokensGenerated > 0 {
		fmt.Printf(" | 🔤 Tokens: %d", metrics.TokensGenerated)
		
		if metrics.TokensPerSecond > 0 {
			fmt.Printf(" (%.1f/s)", metrics.TokensPerSecond)
		}
	}
	
	if metrics.CharactersCount > 0 {
		fmt.Printf(" | 📝 字符: %d", metrics.CharactersCount)
	}
	
	if metrics.ErrorCount > 0 {
		fmt.Printf(" | ❌ 错误: %d", metrics.ErrorCount)
	}
	
	fmt.Println()
}

// GetSummaryStats 获取汇总统计
func (m *Monitor) GetSummaryStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	totalSessions := len(m.metrics)
	totalRequests := 0
	totalErrors := 0
	totalTokens := 0
	totalDuration := time.Duration(0)

	for _, metrics := range m.metrics {
		totalRequests += metrics.RequestCount
		totalErrors += metrics.ErrorCount
		totalTokens += metrics.TokensGenerated
		totalDuration += time.Since(metrics.StartTime)
	}

	stats := map[string]interface{}{
		"active_sessions": totalSessions,
		"total_requests":  totalRequests,
		"total_errors":    totalErrors,
		"total_tokens":    totalTokens,
		"total_duration":  totalDuration,
	}

	if totalRequests > 0 {
		stats["error_rate"] = float64(totalErrors) / float64(totalRequests) * 100
	}

	if totalDuration.Seconds() > 0 && totalTokens > 0 {
		stats["average_tokens_per_second"] = float64(totalTokens) / totalDuration.Seconds()
	}

	return stats
}

// countWords 计算单词数
func countWords(text string) int {
	if text == "" {
		return 0
	}
	
	words := 0
	inWord := false
	
	for _, char := range text {
		if char == ' ' || char == '\t' || char == '\n' || char == '\r' {
			if inWord {
				words++
				inWord = false
			}
		} else {
			inWord = true
		}
	}
	
	if inWord {
		words++
	}
	
	return words
}

// RealtimeMonitor 实时监控器
type RealtimeMonitor struct {
	monitor   *Monitor
	sessionID string
	enabled   bool
}

// NewRealtimeMonitor 创建实时监控器
func NewRealtimeMonitor(monitor *Monitor, sessionID string) *RealtimeMonitor {
	return &RealtimeMonitor{
		monitor:   monitor,
		sessionID: sessionID,
		enabled:   true,
	}
}

// Enable 启用监控
func (rm *RealtimeMonitor) Enable() {
	rm.enabled = true
}

// Disable 禁用监控
func (rm *RealtimeMonitor) Disable() {
	rm.enabled = false
}

// RecordChunk 记录文本块
func (rm *RealtimeMonitor) RecordChunk(text string) {
	if !rm.enabled {
		return
	}
	
	rm.monitor.RecordText(rm.sessionID, text)
}

// RecordTokenChunk 记录token块
func (rm *RealtimeMonitor) RecordTokenChunk(tokens int) {
	if !rm.enabled {
		return
	}
	
	rm.monitor.RecordTokens(rm.sessionID, 0, tokens)
}

// ShowProgress 显示进度
func (rm *RealtimeMonitor) ShowProgress() {
	if !rm.enabled {
		return
	}
	
	metrics := rm.monitor.GetCurrentMetrics(rm.sessionID)
	if metrics != nil && metrics.TokensGenerated > 0 {
		fmt.Printf("\r⚡ %d tokens (%.1f/s)", 
			metrics.TokensGenerated, 
			metrics.TokensPerSecond)
	}
}
