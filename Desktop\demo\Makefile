# Ollama CLI Makefile

# 变量定义
BINARY_NAME=ollama-cli
MAIN_FILE=main.go
BUILD_DIR=build
COVERAGE_FILE=coverage.out

# 默认目标
.PHONY: all
all: clean test build

# 构建二进制文件
.PHONY: build
build:
	@echo "构建 $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_FILE)
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 构建多平台版本
.PHONY: build-all
build-all: clean
	@echo "构建多平台版本..."
	@mkdir -p $(BUILD_DIR)
	
	# Windows
	GOOS=windows GOARCH=amd64 go build -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(MAIN_FILE)
	
	# Linux
	GOOS=linux GOARCH=amd64 go build -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_FILE)
	
	# macOS
	GOOS=darwin GOARCH=amd64 go build -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(MAIN_FILE)
	GOOS=darwin GOARCH=arm64 go build -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 $(MAIN_FILE)
	
	@echo "多平台构建完成"

# 运行测试
.PHONY: test
test:
	@echo "运行测试..."
	go test -v ./...

# 运行测试并生成覆盖率报告
.PHONY: test-coverage
test-coverage:
	@echo "运行测试并生成覆盖率报告..."
	go test -coverprofile=$(COVERAGE_FILE) ./...
	go tool cover -html=$(COVERAGE_FILE) -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 运行基准测试
.PHONY: bench
bench:
	@echo "运行基准测试..."
	go test -bench=. ./...

# 代码格式化
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	go fmt ./...

# 代码检查
.PHONY: vet
vet:
	@echo "运行代码检查..."
	go vet ./...

# 运行所有检查
.PHONY: check
check: fmt vet test
	@echo "所有检查完成"

# 清理构建文件
.PHONY: clean
clean:
	@echo "清理构建文件..."
	rm -rf $(BUILD_DIR)
	rm -f $(COVERAGE_FILE) coverage.html

# 安装依赖
.PHONY: deps
deps:
	@echo "下载依赖..."
	go mod download
	go mod tidy

# 运行示例
.PHONY: run-examples
run-examples:
	@echo "运行配置示例..."
	go run examples/config_example.go
	@echo "\n运行基本使用示例..."
	go run examples/basic_usage.go

# 开发模式运行
.PHONY: dev
dev:
	@echo "开发模式运行..."
	go run $(MAIN_FILE) -verbose

# 显示帮助
.PHONY: help
help:
	@echo "可用的make目标:"
	@echo "  all          - 清理、测试、构建"
	@echo "  build        - 构建二进制文件"
	@echo "  build-all    - 构建多平台版本"
	@echo "  test         - 运行测试"
	@echo "  test-coverage- 运行测试并生成覆盖率报告"
	@echo "  bench        - 运行基准测试"
	@echo "  fmt          - 格式化代码"
	@echo "  vet          - 运行代码检查"
	@echo "  check        - 运行所有检查"
	@echo "  clean        - 清理构建文件"
	@echo "  deps         - 安装依赖"
	@echo "  run-examples - 运行示例代码"
	@echo "  dev          - 开发模式运行"
	@echo "  help         - 显示此帮助信息"
